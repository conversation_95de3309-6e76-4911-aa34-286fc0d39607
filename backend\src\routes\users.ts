import express from 'express';
import { z } from 'zod';
import prisma from '../config/database';
import { authenticate, authorize } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';

const router = express.Router();

// Get current user profile
router.get('/me', authenticate, asyncHandler(async (req, res) => {
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: {
      id: true,
      email: true,
      role: true,
      emailVerified: true,
      createdAt: true,
      developerProfile: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          displayName: true,
          avatarUrl: true,
          country: true,
          city: true,
          bio: true,
          hourlyRate: true,
          currency: true,
          availabilityStatus: true,
          yearsExperience: true,
          githubUsername: true,
          linkedinUrl: true,
          portfolioUrl: true,
          skills: true,
          languages: true,
          verificationStatus: true,
          rating: true,
          totalProjects: true
        }
      },
      businessProfile: {
        select: {
          id: true,
          companyName: true,
          companyLogoUrl: true,
          contactPerson: true,
          contactPhone: true,
          country: true,
          city: true,
          industry: true,
          companySize: true,
          websiteUrl: true,
          description: true,
          verificationStatus: true,
          totalProjects: true
        }
      }
    }
  });

  if (!user) {
    throw createError('User not found', 404);
  }

  res.json({
    success: true,
    data: {
      user: {
        ...user,
        profile: user.developerProfile || user.businessProfile
      }
    }
  });
}));

// Update developer profile
const updateDeveloperProfileSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  displayName: z.string().optional(),
  bio: z.string().optional(),
  hourlyRate: z.number().positive().optional(),
  currency: z.string().length(3).optional(),
  availabilityStatus: z.enum(['available', 'busy', 'unavailable']).optional(),
  yearsExperience: z.number().int().min(0).optional(),
  githubUsername: z.string().optional(),
  linkedinUrl: z.string().url().optional(),
  portfolioUrl: z.string().url().optional(),
  city: z.string().optional(),
  skills: z.array(z.string()).optional(),
  languages: z.array(z.object({
    language: z.string(),
    proficiency: z.enum(['basic', 'conversational', 'fluent', 'native'])
  })).optional()
});

router.put('/me/profile', 
  authenticate, 
  authorize('developer'), 
  asyncHandler(async (req, res) => {
    const validatedData = updateDeveloperProfileSchema.parse(req.body);

    const updatedProfile = await prisma.developerProfile.update({
      where: { userId: req.user!.id },
      data: validatedData
    });

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { profile: updatedProfile }
    });
  })
);

// Update business profile
const updateBusinessProfileSchema = z.object({
  companyName: z.string().min(1).optional(),
  contactPerson: z.string().min(1).optional(),
  contactPhone: z.string().optional(),
  city: z.string().optional(),
  industry: z.string().optional(),
  companySize: z.enum(['SIZE_1_10', 'SIZE_11_50', 'SIZE_51_200', 'SIZE_200_PLUS']).optional(),
  websiteUrl: z.string().url().optional(),
  description: z.string().optional()
});

router.put('/me/business-profile', 
  authenticate, 
  authorize('business'), 
  asyncHandler(async (req, res) => {
    const validatedData = updateBusinessProfileSchema.parse(req.body);

    const updatedProfile = await prisma.businessProfile.update({
      where: { userId: req.user!.id },
      data: validatedData
    });

    res.json({
      success: true,
      message: 'Business profile updated successfully',
      data: { profile: updatedProfile }
    });
  })
);

// Get public developer profiles
router.get('/developers', asyncHandler(async (req, res) => {
  const { page = 1, limit = 10, country, skills, availability } = req.query;
  
  const skip = (Number(page) - 1) * Number(limit);
  const take = Number(limit);

  const where: any = {
    verificationStatus: 'verified'
  };

  if (country) {
    where.country = country;
  }

  if (availability) {
    where.availabilityStatus = availability;
  }

  // Note: Skills filtering would need more complex logic with the skills relation
  
  const [developers, total] = await Promise.all([
    prisma.developerProfile.findMany({
      where,
      skip,
      take,
      select: {
        id: true,
        firstName: true,
        lastName: true,
        displayName: true,
        avatarUrl: true,
        country: true,
        city: true,
        bio: true,
        hourlyRate: true,
        currency: true,
        availabilityStatus: true,
        yearsExperience: true,
        skills: true,
        rating: true,
        totalProjects: true
      },
      orderBy: { createdAt: 'desc' }
    }),
    prisma.developerProfile.count({ where })
  ]);

  res.json({
    success: true,
    data: {
      developers,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    }
  });
}));

// Get public developer profile by ID
router.get('/developers/:id', asyncHandler(async (req, res) => {
  const developer = await prisma.developerProfile.findUnique({
    where: { id: req.params.id },
    select: {
      id: true,
      firstName: true,
      lastName: true,
      displayName: true,
      avatarUrl: true,
      country: true,
      city: true,
      bio: true,
      hourlyRate: true,
      currency: true,
      availabilityStatus: true,
      yearsExperience: true,
      githubUsername: true,
      linkedinUrl: true,
      portfolioUrl: true,
      skills: true,
      languages: true,
      rating: true,
      totalProjects: true,
      createdAt: true
    }
  });

  if (!developer) {
    throw createError('Developer not found', 404);
  }

  res.json({
    success: true,
    data: { developer }
  });
}));

export default router;
