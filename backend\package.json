{"name": "south-safari-backend", "version": "0.1.0", "description": "Backend API for South Safari Developer Partnership Platform", "main": "dist/index.js", "author": "South Safari Team", "license": "UNLICENSED", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "format": "prettier --write 'src/**/*.ts'", "type-check": "tsc --noEmit", "db:migrate": "prisma migrate dev", "db:push": "prisma db push", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio"}, "dependencies": {"@prisma/client": "^5.7.0", "@sendgrid/mail": "^8.1.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "bull": "^4.11.5", "compression": "^1.7.4", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "redis": "^4.6.11", "sharp": "^0.33.0", "stripe": "^14.8.0", "uuid": "^9.0.1", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/nodemailer": "^6.4.14", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@vitejs/plugin-react": "^4.6.0", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "prisma": "^5.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3", "vite": "^7.0.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}