import axios, { AxiosResponse } from 'axios'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const authData = localStorage.getItem('auth-storage')
    if (authData) {
      try {
        const { state } = JSON.parse(authData)
        if (state.accessToken) {
          config.headers.Authorization = `Bearer ${state.accessToken}`
        }
      } catch (error) {
        console.error('Error parsing auth data:', error)
      }
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const authData = localStorage.getItem('auth-storage')
        if (authData) {
          const { state } = JSON.parse(authData)
          if (state.refreshToken) {
            const response = await authApi.refreshToken(state.refreshToken)
            const { tokens } = response.data

            // Update stored tokens
            const updatedAuthData = {
              ...JSON.parse(authData),
              state: {
                ...state,
                accessToken: tokens.accessToken,
                refreshToken: tokens.refreshToken,
              }
            }
            localStorage.setItem('auth-storage', JSON.stringify(updatedAuthData))

            // Retry original request with new token
            originalRequest.headers.Authorization = `Bearer ${tokens.accessToken}`
            return api(originalRequest)
          }
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('auth-storage')
        window.location.href = '/login'
        return Promise.reject(refreshError)
      }
    }

    return Promise.reject(error)
  }
)

// API response interface
interface ApiResponse<T = any> {
  success: boolean
  message?: string
  data: T
}

// Auth API
export const authApi = {
  login: (credentials: { email: string; password: string }): Promise<AxiosResponse<ApiResponse<{ user: any; tokens: any }>>> =>
    api.post('/auth/login', credentials),

  register: (userData: any): Promise<AxiosResponse<ApiResponse<{ user: any; tokens: any }>>> =>
    api.post('/auth/register', userData),

  logout: (): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/auth/logout'),

  refreshToken: (refreshToken: string): Promise<AxiosResponse<ApiResponse<{ tokens: any }>>> =>
    api.post('/auth/refresh', { refreshToken }),
}

// User API
export const userApi = {
  getProfile: (): Promise<AxiosResponse<ApiResponse<{ user: any }>>> =>
    api.get('/users/me'),

  updateProfile: (profileData: any): Promise<AxiosResponse<ApiResponse<{ profile: any }>>> =>
    api.put('/users/me/profile', profileData),

  updateBusinessProfile: (profileData: any): Promise<AxiosResponse<ApiResponse<{ profile: any }>>> =>
    api.put('/users/me/business-profile', profileData),

  getDevelopers: (params?: any): Promise<AxiosResponse<ApiResponse<{ developers: any[]; pagination: any }>>> =>
    api.get('/users/developers', { params }),

  getDeveloper: (id: string): Promise<AxiosResponse<ApiResponse<{ developer: any }>>> =>
    api.get(`/users/developers/${id}`),
}

// Project API
export const projectApi = {
  getProjects: (params?: any): Promise<AxiosResponse<ApiResponse<{ projects: any[]; pagination: any }>>> =>
    api.get('/projects', { params }),

  getProject: (id: string): Promise<AxiosResponse<ApiResponse<{ project: any }>>> =>
    api.get(`/projects/${id}`),

  createProject: (projectData: any): Promise<AxiosResponse<ApiResponse<{ project: any }>>> =>
    api.post('/projects', projectData),

  updateProject: (id: string, projectData: any): Promise<AxiosResponse<ApiResponse<{ project: any }>>> =>
    api.put(`/projects/${id}`, projectData),

  deleteProject: (id: string): Promise<AxiosResponse<ApiResponse>> =>
    api.delete(`/projects/${id}`),
}

export default api
