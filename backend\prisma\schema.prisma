// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  developer
  business
  admin
}

enum ProjectStatus {
  draft
  active
  in_progress
  completed
  cancelled
}

enum PartnershipType {
  fixed
  hourly
  revenue_share
  equity
}

enum ApplicationStatus {
  pending
  shortlisted
  accepted
  rejected
}

enum PartnershipStatus {
  active
  completed
  terminated
  disputed
}

enum MilestoneStatus {
  pending
  in_progress
  submitted
  approved
  rejected
}

enum VerificationStatus {
  pending
  verified
  rejected
}

enum AvailabilityStatus {
  available
  busy
  unavailable
}

enum CompanySize {
  SIZE_1_10    @map("1-10")
  SIZE_11_50   @map("11-50")
  SIZE_51_200  @map("51-200")
  SIZE_200_PLUS @map("200+")
}

enum Visibility {
  public
  private
}

enum ProficiencyLevel {
  beginner
  intermediate
  advanced
  expert
}

// Core Users table
model User {
  id                        String   @id @default(uuid())
  email                     String   @unique
  passwordHash              String   @map("password_hash")
  role                      UserRole
  emailVerified             <PERSON>olean  @default(false) @map("email_verified")
  emailVerificationToken    String?  @map("email_verification_token")
  passwordResetToken        String?  @map("password_reset_token")
  passwordResetExpires      DateTime? @map("password_reset_expires")
  twoFactorSecret           String?  @map("two_factor_secret")
  twoFactorEnabled          Boolean  @default(false) @map("two_factor_enabled")
  lastLogin                 DateTime? @map("last_login")
  createdAt                 DateTime @default(now()) @map("created_at")
  updatedAt                 DateTime @updatedAt @map("updated_at")

  // Relations
  developerProfile          DeveloperProfile?
  businessProfile           BusinessProfile?
  adminProfile              AdminProfile?
  sentMessages              Message[] @relation("MessageSender")
  receivedMessages          Message[] @relation("MessageReceiver")
  reviews                   Review[] @relation("ReviewReviewer")
  reviewsReceived           Review[] @relation("ReviewReviewed")
  notifications             Notification[]
  auditLogs                 AuditLog[]
  projectViews              ProjectView[]

  @@map("users")
}

// Developer profiles
model DeveloperProfile {
  id                  String              @id @default(uuid())
  userId              String              @unique @map("user_id")
  firstName           String              @map("first_name")
  lastName            String              @map("last_name")
  displayName         String?             @map("display_name")
  avatarUrl           String?             @map("avatar_url")
  country             String
  city                String?
  timezone            String?
  bio                 String?
  hourlyRate          Decimal?            @map("hourly_rate")
  currency            String              @default("USD")
  availabilityStatus  AvailabilityStatus  @default(available) @map("availability_status")
  yearsExperience     Int?                @map("years_experience")
  githubUsername      String?             @map("github_username")
  linkedinUrl         String?             @map("linkedin_url")
  portfolioUrl        String?             @map("portfolio_url")
  resumeUrl           String?             @map("resume_url")
  skills              Json?
  languages           Json?
  verificationStatus  VerificationStatus  @default(pending) @map("verification_status")
  verificationDate    DateTime?           @map("verification_date")
  rating              Decimal?
  totalProjects       Int                 @default(0) @map("total_projects")
  totalEarnings       Decimal             @default(0) @map("total_earnings")
  createdAt           DateTime            @default(now()) @map("created_at")
  updatedAt           DateTime            @updatedAt @map("updated_at")

  // Relations
  user                User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  applications        Application[]
  partnerships        Partnership[]
  developerSkills     DeveloperSkill[]

  @@map("developer_profiles")
}

// Business profiles
model BusinessProfile {
  id                  String              @id @default(uuid())
  userId              String              @unique @map("user_id")
  companyName         String              @map("company_name")
  companyLogoUrl      String?             @map("company_logo_url")
  contactPerson       String              @map("contact_person")
  contactPhone        String?             @map("contact_phone")
  country             String
  city                String?
  address             String?
  industry            String?
  companySize         CompanySize?        @map("company_size")
  websiteUrl          String?             @map("website_url")
  description         String?
  taxId               String?             @map("tax_id")
  verificationStatus  VerificationStatus  @default(pending) @map("verification_status")
  verificationDate    DateTime?           @map("verification_date")
  totalProjects       Int                 @default(0) @map("total_projects")
  totalSpent          Decimal             @default(0) @map("total_spent")
  createdAt           DateTime            @default(now()) @map("created_at")
  updatedAt           DateTime            @updatedAt @map("updated_at")

  // Relations
  user                User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  projects            Project[]
  partnerships        Partnership[]

  @@map("business_profiles")
}

// Admin profiles
model AdminProfile {
  id          String   @id @default(uuid())
  userId      String   @unique @map("user_id")
  name        String
  permissions Json?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("admin_profiles")
}

// Projects
model Project {
  id                  String         @id @default(uuid())
  businessId          String         @map("business_id")
  title               String
  slug                String?        @unique
  description         String
  requirements        String?
  category            String
  subcategory         String?
  skillsRequired      Json           @map("skills_required")
  budgetMin           Decimal?       @map("budget_min")
  budgetMax           Decimal?       @map("budget_max")
  currency            String         @default("USD")
  durationWeeks       Int?           @map("duration_weeks")
  status              ProjectStatus  @default(draft)
  partnershipType     PartnershipType @default(fixed) @map("partnership_type")
  visibility          Visibility     @default(public)
  featured            Boolean        @default(false)
  viewsCount          Int            @default(0) @map("views_count")
  applicationsCount   Int            @default(0) @map("applications_count")
  createdAt           DateTime       @default(now()) @map("created_at")
  updatedAt           DateTime       @updatedAt @map("updated_at")
  publishedAt         DateTime?      @map("published_at")
  deadline            DateTime?

  // Relations
  business            BusinessProfile @relation(fields: [businessId], references: [id], onDelete: Cascade)
  applications        Application[]
  partnerships        Partnership[]
  messages            Message[]
  projectViews        ProjectView[]

  @@map("projects")
}

// Applications
model Application {
  id                      String            @id @default(uuid())
  projectId               String            @map("project_id")
  developerId             String            @map("developer_id")
  proposal                String
  coverLetter             String?           @map("cover_letter")
  proposedRate            Decimal?          @map("proposed_rate")
  proposedCurrency        String            @default("USD") @map("proposed_currency")
  estimatedDurationWeeks  Int?              @map("estimated_duration_weeks")
  status                  ApplicationStatus @default(pending)
  cvUrl                   String?           @map("cv_url")
  portfolioItems          Json?             @map("portfolio_items")
  availabilityDate        DateTime?         @map("availability_date")
  reviewedAt              DateTime?         @map("reviewed_at")
  reviewedBy              String?           @map("reviewed_by")
  rejectionReason         String?           @map("rejection_reason")
  createdAt               DateTime          @default(now()) @map("created_at")
  updatedAt               DateTime          @updatedAt @map("updated_at")

  // Relations
  project                 Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  developer               DeveloperProfile  @relation(fields: [developerId], references: [id], onDelete: Cascade)
  partnerships            Partnership[]

  @@unique([projectId, developerId])
  @@map("applications")
}

// Partnerships
model Partnership {
  id                      String            @id @default(uuid())
  projectId               String            @map("project_id")
  developerId             String            @map("developer_id")
  businessId              String            @map("business_id")
  applicationId           String?           @map("application_id")
  contractType            PartnershipType   @map("contract_type")
  agreedRate              Decimal?          @map("agreed_rate")
  agreedCurrency          String            @default("USD") @map("agreed_currency")
  revenueSharePercentage  Decimal?          @map("revenue_share_percentage")
  equityPercentage        Decimal?          @map("equity_percentage")
  startDate               DateTime          @map("start_date")
  endDate                 DateTime?         @map("end_date")
  status                  PartnershipStatus @default(active)
  totalValue              Decimal           @default(0) @map("total_value")
  totalPaid               Decimal           @default(0) @map("total_paid")
  contractUrl             String?           @map("contract_url")
  notes                   String?
  terminatedAt            DateTime?         @map("terminated_at")
  terminatedBy            String?           @map("terminated_by")
  terminationReason       String?           @map("termination_reason")
  createdAt               DateTime          @default(now()) @map("created_at")
  updatedAt               DateTime          @updatedAt @map("updated_at")

  // Relations
  project                 Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  developer               DeveloperProfile  @relation(fields: [developerId], references: [id], onDelete: Cascade)
  business                BusinessProfile   @relation(fields: [businessId], references: [id], onDelete: Cascade)
  application             Application?      @relation(fields: [applicationId], references: [id])
  milestones              Milestone[]
  messages                Message[]
  reviews                 Review[]

  @@map("partnerships")
}

// Milestones
model Milestone {
  id              String          @id @default(uuid())
  partnershipId   String          @map("partnership_id")
  title           String
  description     String?
  deliverables    String?
  dueDate         DateTime        @map("due_date")
  amount          Decimal?
  currency        String          @default("USD")
  status          MilestoneStatus @default(pending)
  startedAt       DateTime?       @map("started_at")
  submittedAt     DateTime?       @map("submitted_at")
  approvedAt      DateTime?       @map("approved_at")
  approvedBy      String?         @map("approved_by")
  rejectionReason String?         @map("rejection_reason")
  attachments     Json?
  createdAt       DateTime        @default(now()) @map("created_at")
  updatedAt       DateTime        @updatedAt @map("updated_at")

  // Relations
  partnership     Partnership     @relation(fields: [partnershipId], references: [id], onDelete: Cascade)

  @@map("milestones")
}

// Messages
model Message {
  id            String       @id @default(uuid())
  senderId      String       @map("sender_id")
  receiverId    String       @map("receiver_id")
  projectId     String?      @map("project_id")
  partnershipId String?      @map("partnership_id")
  subject       String?
  content       String
  attachments   Json?
  isRead        Boolean      @default(false) @map("is_read")
  readAt        DateTime?    @map("read_at")
  isArchived    Boolean      @default(false) @map("is_archived")
  createdAt     DateTime     @default(now()) @map("created_at")

  // Relations
  sender        User         @relation("MessageSender", fields: [senderId], references: [id], onDelete: Cascade)
  receiver      User         @relation("MessageReceiver", fields: [receiverId], references: [id], onDelete: Cascade)
  project       Project?     @relation(fields: [projectId], references: [id], onDelete: SetNull)
  partnership   Partnership? @relation(fields: [partnershipId], references: [id], onDelete: SetNull)

  @@map("messages")
}

// Reviews
model Review {
  id                  String      @id @default(uuid())
  partnershipId       String      @map("partnership_id")
  reviewerId          String      @map("reviewer_id")
  reviewedId          String      @map("reviewed_id")
  rating              Int
  communicationRating Int?        @map("communication_rating")
  qualityRating       Int?        @map("quality_rating")
  timelinessRating    Int?        @map("timeliness_rating")
  comment             String?
  isPublic            Boolean     @default(true) @map("is_public")
  createdAt           DateTime    @default(now()) @map("created_at")
  updatedAt           DateTime    @updatedAt @map("updated_at")

  // Relations
  partnership         Partnership @relation(fields: [partnershipId], references: [id], onDelete: Cascade)
  reviewer            User        @relation("ReviewReviewer", fields: [reviewerId], references: [id], onDelete: Cascade)
  reviewed            User        @relation("ReviewReviewed", fields: [reviewedId], references: [id], onDelete: Cascade)

  @@unique([partnershipId, reviewerId])
  @@map("reviews")
}

// Skills master table
model Skill {
  id              String           @id @default(uuid())
  name            String           @unique
  category        String
  isActive        Boolean          @default(true) @map("is_active")
  usageCount      Int              @default(0) @map("usage_count")
  createdAt       DateTime         @default(now()) @map("created_at")

  // Relations
  developerSkills DeveloperSkill[]

  @@map("skills")
}

// Developer skills (many-to-many)
model DeveloperSkill {
  developerId       String           @map("developer_id")
  skillId           String           @map("skill_id")
  yearsExperience   Int?             @map("years_experience")
  proficiencyLevel  ProficiencyLevel @default(intermediate) @map("proficiency_level")
  isPrimary         Boolean          @default(false) @map("is_primary")
  verified          Boolean          @default(false)
  createdAt         DateTime         @default(now()) @map("created_at")

  // Relations
  developer         DeveloperProfile @relation(fields: [developerId], references: [id], onDelete: Cascade)
  skill             Skill            @relation(fields: [skillId], references: [id], onDelete: Cascade)

  @@id([developerId, skillId])
  @@map("developer_skills")
}

// Project views tracking
model ProjectView {
  id        String   @id @default(uuid())
  projectId String   @map("project_id")
  viewerId  String?  @map("viewer_id")
  ipAddress String?  @map("ip_address")
  userAgent String?  @map("user_agent")
  viewedAt  DateTime @default(now()) @map("viewed_at")

  // Relations
  project   Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  viewer    User?    @relation(fields: [viewerId], references: [id], onDelete: SetNull)

  @@map("project_views")
}

// Notifications
model Notification {
  id        String    @id @default(uuid())
  userId    String    @map("user_id")
  type      String
  title     String
  message   String?
  data      Json?
  isRead    Boolean   @default(false) @map("is_read")
  readAt    DateTime? @map("read_at")
  createdAt DateTime  @default(now()) @map("created_at")

  // Relations
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

// Audit log
model AuditLog {
  id         String    @id @default(uuid())
  userId     String?   @map("user_id")
  action     String
  entityType String?   @map("entity_type")
  entityId   String?   @map("entity_id")
  oldValues  Json?     @map("old_values")
  newValues  Json?     @map("new_values")
  ipAddress  String?   @map("ip_address")
  userAgent  String?   @map("user_agent")
  createdAt  DateTime  @default(now()) @map("created_at")

  // Relations
  user       User?     @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("audit_logs")
}
