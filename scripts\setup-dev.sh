#!/bin/bash

# South Safari Development Environment Setup Script
# This script sets up the development environment for the South Safari platform

echo "🚀 Setting up South Safari Development Environment..."

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p backend/logs
mkdir -p frontend/public
mkdir -p uploads
touch uploads/.gitkeep

# Install backend dependencies
echo "📦 Installing backend dependencies..."
cd backend
npm install

# Generate Prisma client
echo "🔧 Generating Prisma client..."
npx prisma generate

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
cd ../frontend
npm install

# Return to root
cd ..

echo "✅ Development environment setup complete!"
echo ""
echo "Next steps:"
echo "1. Set up PostgreSQL database:"
echo "   - Create database: createdb southsafari"
echo "   - Create user: createuser southsafari --pwprompt"
echo "   - Grant privileges: GRANT ALL PRIVILEGES ON DATABASE southsafari TO southsafari;"
echo ""
echo "2. Run database migrations:"
echo "   cd backend && npx prisma db push"
echo ""
echo "3. Start the development servers:"
echo "   Backend: cd backend && npm run dev"
echo "   Frontend: cd frontend && npm run dev"
echo ""
echo "4. Access the application:"
echo "   Frontend: http://localhost:3000"
echo "   Backend API: http://localhost:5000"
echo "   API Health: http://localhost:5000/health"
