import express from 'express';
import { z } from 'zod';
import prisma from '../config/database';
import { authenticate, authorize, optionalAuth } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';

const router = express.Router();

// Create project schema
const createProjectSchema = z.object({
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  requirements: z.string().optional(),
  category: z.string().min(1, 'Category is required'),
  subcategory: z.string().optional(),
  skillsRequired: z.array(z.string()).min(1, 'At least one skill is required'),
  budgetMin: z.number().positive().optional(),
  budgetMax: z.number().positive().optional(),
  currency: z.string().length(3).default('USD'),
  durationWeeks: z.number().int().positive().optional(),
  partnershipType: z.enum(['fixed', 'hourly', 'revenue_share', 'equity']).default('fixed'),
  deadline: z.string().datetime().optional()
});

// Get all public projects
router.get('/', optionalAuth, asyncHandler(async (req, res) => {
  const { 
    page = 1, 
    limit = 10, 
    category, 
    skills, 
    budgetMin, 
    budgetMax, 
    partnershipType,
    search 
  } = req.query;
  
  const skip = (Number(page) - 1) * Number(limit);
  const take = Number(limit);

  const where: any = {
    status: 'active',
    visibility: 'public'
  };

  if (category) {
    where.category = category;
  }

  if (partnershipType) {
    where.partnershipType = partnershipType;
  }

  if (budgetMin || budgetMax) {
    where.AND = [];
    if (budgetMin) {
      where.AND.push({ budgetMax: { gte: Number(budgetMin) } });
    }
    if (budgetMax) {
      where.AND.push({ budgetMin: { lte: Number(budgetMax) } });
    }
  }

  if (search) {
    where.OR = [
      { title: { contains: search as string, mode: 'insensitive' } },
      { description: { contains: search as string, mode: 'insensitive' } }
    ];
  }

  const [projects, total] = await Promise.all([
    prisma.project.findMany({
      where,
      skip,
      take,
      select: {
        id: true,
        title: true,
        description: true,
        category: true,
        subcategory: true,
        skillsRequired: true,
        budgetMin: true,
        budgetMax: true,
        currency: true,
        durationWeeks: true,
        partnershipType: true,
        viewsCount: true,
        applicationsCount: true,
        createdAt: true,
        publishedAt: true,
        deadline: true,
        business: {
          select: {
            id: true,
            companyName: true,
            companyLogoUrl: true,
            country: true,
            industry: true,
            verificationStatus: true
          }
        }
      },
      orderBy: [
        { featured: 'desc' },
        { publishedAt: 'desc' }
      ]
    }),
    prisma.project.count({ where })
  ]);

  res.json({
    success: true,
    data: {
      projects,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    }
  });
}));

// Get project by ID
router.get('/:id', optionalAuth, asyncHandler(async (req, res) => {
  const project = await prisma.project.findUnique({
    where: { id: req.params.id },
    include: {
      business: {
        select: {
          id: true,
          companyName: true,
          companyLogoUrl: true,
          contactPerson: true,
          country: true,
          city: true,
          industry: true,
          companySize: true,
          websiteUrl: true,
          description: true,
          verificationStatus: true,
          totalProjects: true
        }
      }
    }
  });

  if (!project) {
    throw createError('Project not found', 404);
  }

  // Check if project is accessible
  if (project.visibility === 'private' && 
      (!req.user || (req.user.role !== 'admin' && project.business.userId !== req.user.id))) {
    throw createError('Project not found', 404);
  }

  // Increment view count (only for non-owners)
  if (!req.user || project.business.userId !== req.user.id) {
    await prisma.$transaction([
      prisma.project.update({
        where: { id: project.id },
        data: { viewsCount: { increment: 1 } }
      }),
      prisma.projectView.create({
        data: {
          projectId: project.id,
          viewerId: req.user?.id,
          ipAddress: req.ip,
          userAgent: req.get('User-Agent')
        }
      })
    ]);
  }

  res.json({
    success: true,
    data: { project }
  });
}));

// Create new project
router.post('/', 
  authenticate, 
  authorize('business'), 
  asyncHandler(async (req, res) => {
    const validatedData = createProjectSchema.parse(req.body);

    // Get business profile
    const businessProfile = await prisma.businessProfile.findUnique({
      where: { userId: req.user!.id }
    });

    if (!businessProfile) {
      throw createError('Business profile not found', 404);
    }

    // Generate slug from title
    const slug = validatedData.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '') + '-' + Date.now();

    const project = await prisma.project.create({
      data: {
        ...validatedData,
        slug,
        businessId: businessProfile.id,
        deadline: validatedData.deadline ? new Date(validatedData.deadline) : null,
        publishedAt: new Date()
      },
      include: {
        business: {
          select: {
            id: true,
            companyName: true,
            companyLogoUrl: true,
            country: true,
            industry: true
          }
        }
      }
    });

    res.status(201).json({
      success: true,
      message: 'Project created successfully',
      data: { project }
    });
  })
);

// Update project
router.put('/:id', 
  authenticate, 
  authorize('business'), 
  asyncHandler(async (req, res) => {
    const project = await prisma.project.findUnique({
      where: { id: req.params.id },
      include: { business: true }
    });

    if (!project) {
      throw createError('Project not found', 404);
    }

    // Check ownership
    if (project.business.userId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    const validatedData = createProjectSchema.partial().parse(req.body);

    const updatedProject = await prisma.project.update({
      where: { id: req.params.id },
      data: {
        ...validatedData,
        deadline: validatedData.deadline ? new Date(validatedData.deadline) : undefined
      },
      include: {
        business: {
          select: {
            id: true,
            companyName: true,
            companyLogoUrl: true,
            country: true,
            industry: true
          }
        }
      }
    });

    res.json({
      success: true,
      message: 'Project updated successfully',
      data: { project: updatedProject }
    });
  })
);

// Delete project
router.delete('/:id', 
  authenticate, 
  authorize('business'), 
  asyncHandler(async (req, res) => {
    const project = await prisma.project.findUnique({
      where: { id: req.params.id },
      include: { business: true }
    });

    if (!project) {
      throw createError('Project not found', 404);
    }

    // Check ownership
    if (project.business.userId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    await prisma.project.delete({
      where: { id: req.params.id }
    });

    res.json({
      success: true,
      message: 'Project deleted successfully'
    });
  })
);

export default router;
