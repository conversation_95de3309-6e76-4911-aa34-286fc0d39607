import { useAuthStore } from '../stores/authStore'

const DashboardPage = () => {
  const { user } = useAuthStore()

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">
          Welcome back, {user?.role === 'developer' ? 'Developer' : 'Business Owner'}!
        </h1>
        <p className="text-gray-600 mt-2">
          Here's what's happening with your {user?.role === 'developer' ? 'applications' : 'projects'}.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {user?.role === 'developer' ? 'Active Applications' : 'Active Projects'}
          </h3>
          <p className="text-3xl font-bold text-primary-600">0</p>
        </div>
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {user?.role === 'developer' ? 'Completed Projects' : 'Hired Developers'}
          </h3>
          <p className="text-3xl font-bold text-secondary-600">0</p>
        </div>
        <div className="card">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {user?.role === 'developer' ? 'Total Earnings' : 'Total Spent'}
          </h3>
          <p className="text-3xl font-bold text-accent-600">$0</p>
        </div>
      </div>

      <div className="card">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">Getting Started</h2>
        <div className="space-y-4">
          {user?.role === 'developer' ? (
            <>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-primary-600 font-semibold">1</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Complete your profile</h3>
                  <p className="text-gray-600 text-sm">Add your skills, experience, and portfolio</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 font-semibold">2</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Browse projects</h3>
                  <p className="text-gray-600 text-sm">Find projects that match your skills</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 font-semibold">3</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Submit proposals</h3>
                  <p className="text-gray-600 text-sm">Apply to projects with compelling proposals</p>
                </div>
              </div>
            </>
          ) : (
            <>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-primary-600 font-semibold">1</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Complete your company profile</h3>
                  <p className="text-gray-600 text-sm">Add company details and verification</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 font-semibold">2</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Post your first project</h3>
                  <p className="text-gray-600 text-sm">Describe your project requirements clearly</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 font-semibold">3</span>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Review applications</h3>
                  <p className="text-gray-600 text-sm">Find the perfect developer for your project</p>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  )
}

export default DashboardPage
