import express, { Request, Response } from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { z } from 'zod';
import prisma from '../config/database';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router = express.Router();

// Validation schemas
const registerSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  role: z.enum(['developer', 'business'], {
    errorMap: () => ({ message: 'Role must be either developer or business' })
  }),
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  companyName: z.string().min(1, 'Company name is required').optional(),
  contactPerson: z.string().min(1, 'Contact person is required').optional(),
  country: z.string().min(1, 'Country is required')
});

const loginSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required')
});

// JWT utility functions
const generateTokens = (userId: string, role: string) => {
  const jwtSecret = process.env.JWT_SECRET;
  const jwtRefreshSecret = process.env.JWT_REFRESH_SECRET;

  if (!jwtSecret || !jwtRefreshSecret) {
    throw new Error('JWT secrets not configured');
  }

  const accessToken = jwt.sign(
    { userId, role },
    jwtSecret,
    { expiresIn: process.env.JWT_EXPIRES_IN || '15m' }
  );

  const refreshToken = jwt.sign(
    { userId, role },
    jwtRefreshSecret,
    { expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d' }
  );

  return { accessToken, refreshToken };
};

// Register endpoint
router.post('/register', asyncHandler(async (req: Request, res: Response) => {
  // Validate input
  const validatedData = registerSchema.parse(req.body);
  const { email, password, role, firstName, lastName, companyName, contactPerson, country } = validatedData;

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email }
  });

  if (existingUser) {
    throw createError('User with this email already exists', 409);
  }

  // Hash password
  const saltRounds = 12;
  const passwordHash = await bcrypt.hash(password, saltRounds);

  // Create user and profile in transaction
  const result = await prisma.$transaction(async (tx) => {
    // Create user
    const user = await tx.user.create({
      data: {
        email,
        passwordHash,
        role: role as any,
        emailVerified: false
      }
    });

    // Create role-specific profile
    if (role === 'developer') {
      await tx.developerProfile.create({
        data: {
          userId: user.id,
          firstName: firstName!,
          lastName: lastName!,
          country
        }
      });
    } else if (role === 'business') {
      await tx.businessProfile.create({
        data: {
          userId: user.id,
          companyName: companyName!,
          contactPerson: contactPerson!,
          country
        }
      });
    }

    return user;
  });

  // Generate tokens
  const { accessToken, refreshToken } = generateTokens(result.id, result.role);

  // Log registration
  logger.info('User registered successfully', {
    userId: result.id,
    email: result.email,
    role: result.role
  });

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    data: {
      user: {
        id: result.id,
        email: result.email,
        role: result.role,
        emailVerified: result.emailVerified
      },
      tokens: {
        accessToken,
        refreshToken
      }
    }
  });
}));

// Login endpoint
router.post('/login', asyncHandler(async (req: Request, res: Response) => {
  // Validate input
  const { email, password } = loginSchema.parse(req.body);

  // Find user with profile
  const user = await prisma.user.findUnique({
    where: { email },
    include: {
      developerProfile: true,
      businessProfile: true,
      adminProfile: true
    }
  });

  if (!user) {
    throw createError('Invalid email or password', 401);
  }

  // Verify password
  const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
  if (!isPasswordValid) {
    throw createError('Invalid email or password', 401);
  }

  // Update last login
  await prisma.user.update({
    where: { id: user.id },
    data: { lastLogin: new Date() }
  });

  // Generate tokens
  const { accessToken, refreshToken } = generateTokens(user.id, user.role);

  // Log login
  logger.info('User logged in successfully', {
    userId: user.id,
    email: user.email,
    role: user.role
  });

  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        emailVerified: user.emailVerified,
        profile: user.developerProfile || user.businessProfile || user.adminProfile
      },
      tokens: {
        accessToken,
        refreshToken
      }
    }
  });
}));

// Refresh token endpoint
router.post('/refresh', asyncHandler(async (req: Request, res: Response) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw createError('Refresh token is required', 400);
  }

  try {
    const jwtRefreshSecret = process.env.JWT_REFRESH_SECRET;
    if (!jwtRefreshSecret) {
      throw new Error('JWT refresh secret not configured');
    }

    const decoded = jwt.verify(refreshToken, jwtRefreshSecret) as any;
    const { accessToken, refreshToken: newRefreshToken } = generateTokens(decoded.userId, decoded.role);

    res.json({
      success: true,
      data: {
        tokens: {
          accessToken,
          refreshToken: newRefreshToken
        }
      }
    });
  } catch (error) {
    throw createError('Invalid refresh token', 401);
  }
}));

// Logout endpoint
router.post('/logout', (_req: Request, res: Response) => {
  // In a real implementation, you might want to blacklist the token
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

export default router;
